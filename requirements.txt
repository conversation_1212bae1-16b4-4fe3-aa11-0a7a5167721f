# EXISTING DEPENDENCIES (UNCHANGED)
asgiref==3.9.1
async-timeout==5.0.1
attrs==25.3.0
bcrypt==4.3.0
dj-database-url==3.0.1
Django==4.2.23
django-cors-headers==4.3.1
django-debug-toolbar==6.0.0
django-extensions==4.1
django-filter==23.3
django-redis==6.0.0
djangorestframework==3.14.0
djangorestframework-simplejwt==5.3.0
drf-spectacular==0.28.0
gunicorn==23.0.0
inflection==0.5.1
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
packaging==25.0
phonenumbers==8.12.42
psycopg2-binary==2.9.10
PyJWT==2.10.1
python-decouple==3.8
pytz==2025.2
PyYAML==6.0.2
redis==6.3.0
referencing==0.36.2
rpds-py==0.26.0
setuptools==80.9.0
sqlparse==0.5.3
typing_extensions==4.14.1
uritemplate==4.2.0
whitenoise==6.9.0

# 🚀 NEW: AI & ASYNC PROCESSING DEPENDENCIES
# Celery for distributed task processing
celery==5.4.0
django-celery-results==2.5.1
django-celery-beat==2.7.0

# LLM API Integration
openai==1.54.3
anthropic==0.40.0


# Enhanced Caching & Performance
django-extensions==4.1
python-dotenv==1.0.1

# Development & Monitoring (Optional)
flower==2.0.1  # Celery monitoring dashboard
django-silk==5.3.0  # Performance profiling (development only)