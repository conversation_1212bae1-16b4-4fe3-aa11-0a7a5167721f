#!/usr/bin/env python3
"""
Test script for Anonymous Feedback API endpoints
Tests the complete flow from session creation to feedback submission
"""

import requests
import json
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000"
HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

def test_anonymous_session_creation():
    """Test creating an anonymous session"""
    print("🔄 Testing Anonymous Session Creation...")
    
    url = f"{API_BASE_URL}/api/auth/anonymous/"
    data = {"county_id": 1}  # Assuming county ID 1 exists
    
    try:
        response = requests.post(url, json=data, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Session created successfully!")
            print(f"   Session ID: {result.get('session_id')}")
            print(f"   Expires in: {result.get('expires_in')} seconds")
            print(f"   Max submissions: {result.get('max_submissions')}")
            return result.get('session_id')
        else:
            print(f"❌ Failed to create session: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error creating session: {e}")
        return None

def test_session_status(session_id):
    """Test checking session status"""
    print(f"\n🔄 Testing Session Status Check for: {session_id}")
    
    url = f"{API_BASE_URL}/api/auth/anonymous/{session_id}/status/"
    
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Session status retrieved successfully!")
            print(f"   Can submit: {result.get('can_submit')}")
            print(f"   Submissions used: {result.get('submissions_used')}")
            print(f"   Submissions limit: {result.get('submissions_limit')}")
            print(f"   Message: {result.get('message')}")
            return result
        else:
            print(f"❌ Failed to get session status: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking session status: {e}")
        return None

def test_anonymous_feedback_submission(session_id):
    """Test submitting anonymous feedback"""
    print(f"\n🔄 Testing Anonymous Feedback Submission...")
    
    url = f"{API_BASE_URL}/api/feedback/anonymous/"
    data = {
        "session_id": session_id,
        "title": "Test Anonymous Feedback",
        "content": "This is a test feedback submission through the anonymous system. Testing the complete flow from session creation to feedback submission.",
        "category": "infrastructure",
        "priority": "medium",
        "county_id": 1
    }
    
    try:
        response = requests.post(url, json=data, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Feedback submitted successfully!")
            print(f"   Tracking ID: {result.get('data', {}).get('tracking_id')}")
            print(f"   Feedback ID: {result.get('data', {}).get('feedback_id')}")
            print(f"   Status: {result.get('data', {}).get('status')}")
            return result.get('data', {}).get('tracking_id')
        else:
            print(f"❌ Failed to submit feedback: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error submitting feedback: {e}")
        return None

def test_feedback_tracking(tracking_id):
    """Test tracking feedback status"""
    print(f"\n🔄 Testing Feedback Tracking for: {tracking_id}")
    
    url = f"{API_BASE_URL}/api/feedback/track/{tracking_id}/"
    
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Feedback tracking successful!")
            print(f"   Status: {result.get('status')}")
            print(f"   Title: {result.get('title')}")
            print(f"   Submitted: {result.get('submitted_at')}")
            print(f"   Location: {result.get('location_path')}")
            return result
        else:
            print(f"❌ Failed to track feedback: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error tracking feedback: {e}")
        return None

def test_counties_endpoint():
    """Test getting available counties"""
    print("\n🔄 Testing Counties Endpoint...")
    
    url = f"{API_BASE_URL}/api/locations/counties/"
    
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Counties retrieved successfully!")
            counties = result.get('data', [])
            print(f"   Found {len(counties)} counties")
            if counties:
                print(f"   First county: {counties[0].get('name')} (ID: {counties[0].get('id')})")
            return counties
        else:
            print(f"❌ Failed to get counties: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Error getting counties: {e}")
        return []

def test_feedback_categories():
    """Test getting feedback categories"""
    print("\n🔄 Testing Feedback Categories Endpoint...")
    
    url = f"{API_BASE_URL}/api/feedback/categories/"
    
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Categories retrieved successfully!")
            categories = result.get('data', [])
            print(f"   Found {len(categories)} categories")
            if categories:
                print(f"   Categories: {[cat.get('label') for cat in categories[:3]]}")
            return categories
        else:
            print(f"❌ Failed to get categories: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Error getting categories: {e}")
        return []

def main():
    """Run all tests"""
    print("=" * 60)
    print("🚀 ANONYMOUS FEEDBACK API TEST SUITE")
    print("=" * 60)
    print(f"Testing against: {API_BASE_URL}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test supporting endpoints first
    counties = test_counties_endpoint()
    categories = test_feedback_categories()
    
    if not counties:
        print("\n❌ Cannot proceed without counties data")
        return
    
    # Test main anonymous feedback flow
    session_id = test_anonymous_session_creation()
    
    if not session_id:
        print("\n❌ Cannot proceed without valid session")
        return
    
    # Check session status
    status = test_session_status(session_id)
    
    if not status or not status.get('can_submit'):
        print("\n❌ Session cannot submit feedback")
        return
    
    # Submit feedback
    tracking_id = test_anonymous_feedback_submission(session_id)
    
    if not tracking_id:
        print("\n❌ Failed to submit feedback")
        return
    
    # Track the submitted feedback
    test_feedback_tracking(tracking_id)
    
    # Check session status after submission
    print(f"\n🔄 Checking session status after submission...")
    final_status = test_session_status(session_id)
    
    if final_status:
        print(f"   Submissions used: {final_status.get('submissions_used')}")
        print(f"   Can still submit: {final_status.get('can_submit')}")
    
    print("\n" + "=" * 60)
    print("✅ ANONYMOUS FEEDBACK API TEST COMPLETED")
    print("=" * 60)
    print(f"Session ID: {session_id}")
    print(f"Tracking ID: {tracking_id}")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
