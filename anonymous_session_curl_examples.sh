#!/bin/bash

# =============================================================================
# CivicAI Anonymous Session API - cURL Examples
# =============================================================================

# Configuration
API_BASE_URL="http://localhost:8000"  # Change to https://api.civicai.ke for production
CONTENT_TYPE="Content-Type: application/json"

echo "=== CivicAI Anonymous Session API Examples ==="
echo "Base URL: $API_BASE_URL"
echo ""

# =============================================================================
# Example 1: Create Anonymous Session (Success)
# =============================================================================
echo "1. Creating Anonymous Session for Nairobi County (ID: 1)"
echo "Request:"
echo "POST $API_BASE_URL/api/auth/anonymous/"

RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/auth/anonymous/" \
  -H "$CONTENT_TYPE" \
  -d '{
    "county_id": 1
  }')

echo "Response:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# Extract session_id for subsequent requests
SESSION_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin).get('session_id', ''))" 2>/dev/null)

# =============================================================================
# Example 2: Create Anonymous Session (Different County)
# =============================================================================
echo "2. Creating Anonymous Session for Mombasa County (ID: 2)"
echo "Request:"
echo "POST $API_BASE_URL/api/auth/anonymous/"

curl -s -X POST "$API_BASE_URL/api/auth/anonymous/" \
  -H "$CONTENT_TYPE" \
  -d '{
    "county_id": 2
  }' | python3 -m json.tool 2>/dev/null

echo ""

# =============================================================================
# Example 3: Invalid County ID (Error Case)
# =============================================================================
echo "3. Attempting to create session with invalid county ID"
echo "Request:"
echo "POST $API_BASE_URL/api/auth/anonymous/"

curl -s -X POST "$API_BASE_URL/api/auth/anonymous/" \
  -H "$CONTENT_TYPE" \
  -d '{
    "county_id": 999999
  }' | python3 -m json.tool 2>/dev/null

echo ""

# =============================================================================
# Example 4: Missing County ID (Error Case)
# =============================================================================
echo "4. Attempting to create session without county ID"
echo "Request:"
echo "POST $API_BASE_URL/api/auth/anonymous/"

curl -s -X POST "$API_BASE_URL/api/auth/anonymous/" \
  -H "$CONTENT_TYPE" \
  -d '{}' | python3 -m json.tool 2>/dev/null

echo ""

# =============================================================================
# Example 5: Check Session Status (if session was created)
# =============================================================================
if [ ! -z "$SESSION_ID" ]; then
    echo "5. Checking session status for: $SESSION_ID"
    echo "Request:"
    echo "GET $API_BASE_URL/api/auth/anonymous/$SESSION_ID/status/"
    
    curl -s -X GET "$API_BASE_URL/api/auth/anonymous/$SESSION_ID/status/" | python3 -m json.tool 2>/dev/null
    echo ""
else
    echo "5. Skipping session status check (no valid session created)"
    echo ""
fi

# =============================================================================
# Example 6: Check Status of Non-existent Session
# =============================================================================
echo "6. Checking status of non-existent session"
echo "Request:"
echo "GET $API_BASE_URL/api/auth/anonymous/ANON_nonexistent/status/"

curl -s -X GET "$API_BASE_URL/api/auth/anonymous/ANON_nonexistent/status/" | python3 -m json.tool 2>/dev/null
echo ""

# =============================================================================
# Example 7: Rate Limiting Test (Multiple Rapid Requests)
# =============================================================================
echo "7. Testing rate limiting (sending 5 rapid requests)"
echo "Note: Rate limit is 100/hour for anonymous users"

for i in {1..5}; do
    echo "Request $i:"
    RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST "$API_BASE_URL/api/auth/anonymous/" \
      -H "$CONTENT_TYPE" \
      -d '{
        "county_id": 1
      }')
    
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    BODY=$(echo "$RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')
    
    echo "Status: $HTTP_CODE"
    if [ "$HTTP_CODE" = "429" ]; then
        echo "Rate limited!"
        echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
        break
    else
        echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
    fi
    echo ""
    sleep 1
done

# =============================================================================
# Example 8: Production Environment Example
# =============================================================================
echo "8. Example for Production Environment"
echo "To use with production API, change the base URL:"
echo ""
echo "curl -X POST 'https://api.civicai.ke/api/auth/anonymous/' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{"
echo "    \"county_id\": 1"
echo "  }'"
echo ""

# =============================================================================
# Example 9: Using with jq for Better JSON Parsing
# =============================================================================
echo "9. Enhanced example using jq (if available)"
echo "If you have jq installed, you can parse responses more elegantly:"
echo ""
echo "# Create session and extract session_id"
echo "SESSION_ID=\$(curl -s -X POST '$API_BASE_URL/api/auth/anonymous/' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"county_id\": 1}' | jq -r '.session_id')"
echo ""
echo "# Check session status"
echo "curl -s -X GET \"$API_BASE_URL/api/auth/anonymous/\$SESSION_ID/status/\" | jq"
echo ""

# =============================================================================
# Example 10: Integration with Frontend JavaScript
# =============================================================================
echo "10. JavaScript/Frontend Integration Example"
echo ""
cat << 'EOF'
// JavaScript example for frontend integration
async function createAnonymousSession(countyId) {
  try {
    const response = await fetch('/api/auth/anonymous/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        county_id: countyId
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Store session_id for later use
      localStorage.setItem('anonymous_session_id', data.session_id);
      console.log('Session created:', data.session_id);
      console.log('Expires in:', data.expires_in, 'seconds');
      console.log('Max submissions:', data.max_submissions);
      return data.session_id;
    } else {
      console.error('Session creation failed:', data.errors);
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}

// Usage
createAnonymousSession(1); // Create session for county ID 1
EOF

echo ""
echo "=== End of Examples ==="
echo ""
echo "💡 Tips:"
echo "- Save the session_id from successful responses"
echo "- Sessions expire after 2 hours (7200 seconds)"
echo "- Maximum 3 submissions per session"
echo "- Rate limit: 100 requests per hour for anonymous users"
echo "- Use HTTPS in production: https://api.civicai.ke"
