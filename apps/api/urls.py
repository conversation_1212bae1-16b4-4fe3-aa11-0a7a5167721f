# =============================================================================
# FILE: apps/api/urls.py (FIXED URL STRUCTURE)
# =============================================================================
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views
from apps.feedback import views as feedback_views
from apps.ai import views as ai_views

app_name = 'api'

urlpatterns = [
    # Health check
    path('health/', views.system_health, name='health'),
    
    # Authentication endpoints
    path('auth/register/', views.RegisterView.as_view(), name='register'),
    path('auth/login/', views.LoginView.as_view(), name='login'),
    path('auth/logout/', views.LogoutView.as_view(), name='logout'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/profile/', views.UserProfileView.as_view(), name='profile'),
    path('auth/anonymous/', views.AnonymousSessionView.as_view(), name='anonymous_session'),
    path('auth/anonymous/<str:session_id>/status/', views.anonymous_session_status, name='anonymous_status'),
    
    # Location endpoints  
    path('locations/counties/', views.CountyListView.as_view(), name='counties'),
    path('locations/hierarchy/', views.LocationHierarchyView.as_view(), name='hierarchy'),
    
    # Feedback endpoints
    path('feedback/submit/', feedback_views.FeedbackSubmissionView.as_view(), name='submit'),
    path('feedback/anonymous/', feedback_views.AnonymousFeedbackView.as_view(), name='anonymous_submit'),
    path('feedback/track/<str:tracking_id>/', feedback_views.track_feedback, name='track'),
    path('feedback/categories/', feedback_views.feedback_categories, name='categories'),
    path('feedback/my-submissions/', feedback_views.UserFeedbackListView.as_view(), name='user_feedback_list'),
    path('feedback/my-submissions/<uuid:id>/', feedback_views.UserFeedbackDetailView.as_view(), name='user_feedback_detail'),
    path('feedback/my-submissions/<uuid:id>/edit/', feedback_views.UserFeedbackUpdateView.as_view(), name='user_feedback_update'),
    path('feedback/my-submissions/<uuid:id>/delete/', feedback_views.UserFeedbackDeleteView.as_view(), name='user_feedback_delete'),
    path('feedback/my-stats/', feedback_views.user_feedback_statistics, name='user_feedback_stats'),
    
    # AI endpoints
    path('ai/dashboard/', ai_views.CountyAIDashboardView.as_view(), name='ai-dashboard'),
    path('ai/feedback/<uuid:feedback_id>/suggestions/', ai_views.get_ai_response_suggestions, name='ai-response-suggestions'),
    path('ai/analytics/', ai_views.get_county_ai_analytics, name='ai-analytics'),
    path('ai/health/', ai_views.ai_system_health, name='ai-health'),
    path('ai/stats/', ai_views.ai_processing_stats, name='ai-stats'),
    path('ai/tasks/<str:task_id>/status/', ai_views.check_ai_task_status, name='ai-task-status'),
    path('ai/feedback/<uuid:feedback_id>/process/', ai_views.trigger_feedback_ai_processing, name='ai-trigger-processing'),
]
